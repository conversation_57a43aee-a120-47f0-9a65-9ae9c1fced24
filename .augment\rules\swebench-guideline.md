---
type: "always_apply"
---

# SWE-Bench - Trainer Workflow Guideline V2

## Table of Contents

1. [Introduction](#introduction)
   - [A High-Level Context for SWE-Bench Quality Annotation](#a-high-level-context-for-swe-bench-quality-annotation)
   - [What is SWE-Bench?](#what-is-swe-bench)
   - [Why Your Role is Important: The Need for Quality Checks](#why-your-role-is-important-the-need-for-quality-checks)
   - [Your Annotation Tasks: Key Areas of Focus](#your-annotation-tasks-key-areas-of-focus)
2. [Rating Dimensions](#rating-dimensions)
3. [The Needed Annotations](#the-needed-annotations)
4. [Early Rejection Reasons](#early-rejection-reasons)
5. [Issue Clarity](#issue-clarity)
6. [Test to Issue Alignment](#test-to-issue-alignment)
7. [Hints](#hints)
8. [Final Verdict](#final-verdict)
9. [Deprecated Checks](#deprecated-checks)
10. [Common Mistakes During Review](#common-mistakes-during-review)

---

## Introduction

### A High-Level Context for SWE-Bench Quality Annotation

Welcome to the SWE-Bench quality check annotation task! Your work is crucial in ensuring that SWE-Bench remains a reliable and effective benchmark for evaluating large language models (LLMs) on real-world software engineering tasks. This document provides a high-level overview to guide you in this important endeavor.

### What is SWE-Bench?

At its core, SWE-Bench is a benchmark designed to test the capabilities of LLMs to resolve actual software engineering issues sourced from popular open-source repositories on GitHub. Each task in SWE-Bench consists of a real-world problem, presented as a GitHub issue, and the corresponding code changes (a "patch") that fix the issue. The goal is to see if an LLM can, given the issue description and the project's codebase, generate a patch that successfully resolves the problem.

The evaluation process is automated: a generated patch is applied to the codebase, and a series of tests are run. If the tests pass, the issue is considered resolved.

### Why Your Role is Important: The Need for Quality Checks

While the process is automated, the quality of the benchmark itself relies on human intelligence. Not all software engineering tasks are created equal. Some GitHub issues are well-defined and have clear solutions, while others can be ambiguous, have inadequate tests, or are simply too complex for the current generation of LLMs.

Your role as a human annotator is to perform a quality check on the tasks within SWE-Bench. By carefully evaluating each task, you help to create a "verified" subset of the benchmark that is fair, consistent, and accurately measures the problem-solving abilities of LLMs. This verified dataset is essential for researchers and developers working to advance the field of AI-powered software engineering.

### Your Annotation Tasks: Key Areas of Focus

As you review each SWE-Bench task, you will be asked to assess it based on the following key criteria:

1. **Clarity of the Issue**: How well is the problem described in the GitHub issue? Is the description clear and unambiguous? Does it provide enough information for a developer (or an LLM) to understand the required changes?

2. **Relevance and Adequacy of Tests**: Do the provided tests accurately reflect the problem described in the issue? Are they comprehensive enough to validate a correct solution without being overly specific or brittle? In other words, would a valid, alternative solution still pass the tests? Specifically, does the test rely on a new function, variable, or, in this case, an accessor method that was created as part of the solution but was not requested or implied in the issue? If a test requires the use of a newly created and unspecified accessor method, it should be flagged as having a test that is too specific or "cheating" by having access to information not available from the problem description.

3. **Overall Solvability**: Based on the clarity of the issue and the fairness of the tests, annotators make a judgment on the overall solvability of the problem. An issue that requires guessing the name of a newly introduced method to pass the tests would be deemed unsolvable from the provided context.

By providing your expert judgment on these aspects, you will contribute directly to the quality and scientific validity of SWE-Bench, helping to shape the future of automated software development.

---

## Rating Dimensions

## The Needed Annotations

### early_reject

- **reject**: `<YES or NO>`
- **reason**: `<if reject = YES: choose one of the 11 options listed below in the Early rejection reasons>`
- **test_case**: `<if reason is between 1 and 6: provide the exact P2P/F2P test case>`

### issue_clarity

- **score**: `<score_0_to_3>`
- **reasoning**: `<Your detailed reasoning for the issue clarity score. Please include references to specific filenames, function/class names, or lines of code where relevant.>`

### test_to_issue_alignment

- **score**: `<score_0_to_3>`
- **failure_category**: `<Required only if score = 2. Select one of the following options: False Negative, False Positive>`
- **failure_test_case**: `<Required only if score >= 2. Provide the test case full name (as in p2p/f2p) which falls into the failure_category specified above>`
- **reasoning**: `<Your detailed reasoning for the test to issue alignment score.>`

### hints

- **is_needed**: `<YES or NO>`
- **reasoning**: `<Your detailed reasoning for why hint is needed. Need to specify if all 3 criteria are met>`
- **value**: `<The resulting hints, populated from the template>`

### final_verdict

- **verdict**: `<ACCEPT or REJECT>`
- **verdict_summary**: `<A one-sentence summary explaining the final verdict. e.g., 'REJECT: The test patch unfairly relies on a new accessor method not mentioned in the issue.'>`

---

## Early Rejection Reasons

1. At least one F2P / P2P test name contains variable values (e.g. timestamp value)
2. At least one F2P / P2P test name is duplicated (present 2 times in the same logs)
3. At least one failed test in base is present in P2P
4. At least one failed test in after is present in F2P / P2P
5. At least one F2P test is present and successful in before
6. At least one P2P, that is missing in base, is not passing in before
   - If a P2P passed in \_base, then you don't have to check same P2P in \_before → this P2P does not satisfy this rejection reason
   - If a P2P is not found in \_base, then you should check it in \_before:
     - If this P2P is passing in \_before → does not satisfy this rejection reason
     - If this P2P was not found in \_before, or failed in \_before → then it satisfies this rejection reason → reject
7. Empty FAIL_TO_PASS
8. PR has more than 15 test files
9. PR has more than 50 updated files
10. Empty log file (except \_post_agent_patch.log)
11. Harness failure
    - E.g.: base/after failed due to bad execution command, issue/pr link not accessible, issue link is actually a pr.

**Note**: Points 1 and 2 are only applicable to JavaScript/TypeScript.

---

## Issue Clarity

> **Important**: problem_statement = issue description only (issue title is not part of it, so ignore it)

**Note**: If the problem_statement contains non-English content, translate before review.

Consider whether the issue description is clear and specified. Following the rating guideline below:

- Scores are assigned such that lower scores reflect higher data quality.
- Scored in 4 categories, label ranging [0, 1, 2, 3] in increasing severity.
- Labels 0 and 1 are minor (acceptable quality)
- Labels 2 and 3 are severe (unacceptable quality) → reject

### Scoring Criteria

- **0**: The issue is well-specified, and it is clear what is required for a successful solution.
- **1**: There are some blanks to fill in about the issue, but there is a sensible interpretation of what is required for a successful solution.
- **2**: The issue is vague, and there is room for ambiguity. It is unclear what a successful solution would look like.
- **3**: It is almost impossible to understand what you are being asked to do without further information.

> **DO NOT LOOK AT THE PR DESCRIPTION, ONLY THE ISSUE DESCRIPTION**

---

## Test to Issue Alignment

**Issue**: Problem_statement

If there are links (doc, issues, image, videos, discussions) in the issue description (not the comments), we'll also check that, but only at 1 level.

Consider whether the test patch is complete and valid. Following the rating guideline below:

- Scores are assigned such that lower scores reflect higher data quality.
- Scored in 4 categories, label ranging [0, 1, 2, 3] in increasing severity.
- Labels 0 and 1 are minor (acceptable quality)
- Labels 2 and 3 are severe (unacceptable quality) → reject

### Scoring Criteria

- **0**: The tests perfectly cover all possible solutions.
- **1**: The tests cover the majority of correct solutions; however, some unusual solutions may be missed.
  - Can cause False Positive (e.g., The tests cover the majority of correct solutions, but are too lenient, allowing an incorrect or incomplete code patch to pass.)
  - Example: The test covers integers calculation, but misses the double/float calculation. Then the test is still usable.
- **2**: The tests work, but some perfectly reasonable solutions may be missed by the tests.
  - Can cause False Negative (e.g., The tests are too strict or specific, failing valid solutions that correctly solve the issue but perhaps in a slightly different way than the original pull request.)
- **3**: The tests are Out-of-scope, which means they look for something different than what the issue is about. (e.g., The tests check for functionality that was not requested or described in the original issue)

---

## Hints

### Step 1: Decide if hints are needed

When a test requires a new accessor method not clearly specified (with method name, parameters, path) in the issue, the hint should provide the exact function header from the gold patch. This includes the function name, its input/output parameters (like self), and any type annotations.

This ensures the model creates a method with the correct signature that the test can call, without leaking implementation details.

#### When to Add a Hint

A hint is required only if **all** of the following conditions are met:

1. **New/updated Accessor**: The golden patch (PR changes) adds a new method/property or updates to existing method/property's signature (name, input/output parameters) to a class whose main purpose is to expose an internal value for testing.

2. **Used by Gold Test**: The corresponding test patch (Unit tests) uses this new accessor to make its assertions.

3. **Not in Issue Description**: The issue description does not already mention or imply the need for this accessor.
   - 3 parts: function name, input type, return type needs to be specified or strongly implied in issue_description. If not, still add hints
   - If function name is mentioned, but input or return type is missing or is not implied, still add hints

### Step 2: Add a Hint, if applicable

#### Template:

```
**IMPORTANT NOTE**
Your code will be tested against some test suite, which has dependency on the following items. Please make sure your code will export and include (but not limited to) the following items:

* **In file `<file_path_1>`:**
    * Must have the trait: `<trait_header>`:
        * Must have the method: `<function_signature_1>`
    * Must have the struct: `<struct_header>`:
        * Must define the field: `<field_name_and_type>`
        * Must implement the method: `<function_signature_2>`
        * Must panic: `<exception_definition_1>`
        * Must return the error: `<error_definition>`
        * Must implement the trait: `<trait_name>`
    * Must define the type: `<type>`
    * Must define the variable: `<variable_definition>`
    * Must have the standalone function: `<function_signature_4>`
    * Must have the enum: `<enum_header>`:
        * Must have the variant: `<variant_name_and_type>`
        * Must implement the method: `<function_signature_5>`
        * Must panic: `<panic_message>`
        * Must return the error: `<error_definition>`
```

- Use `Must raise the exception` in case of panic
- Use `Must return the error` in case of error returned as result

#### How to Populate the Template:

**NOTE**: You don't have to use all of the sections from the above template. Use those that are relevant to your language.

- `<file_path>`: Replace with the file path (with name) being modified or added.
- `<class_header>`: Replace with the header of the class being modified or added.
- `<property_name>` / `<function_signature>`: Copy only the def line of the accessor method from the gold patch.
  - **Isolate the Header**: Copy the def line of the accessor method exactly as it appears in the golden patch. Include the name, self, all other parameters, and any type annotations (-> return_type).
  - **Exclude Decorators**: Do not include any decorators like @property or @staticmethod, as this intentionally leaves the decision of how to implement the accessor (as a standard method vs. a property) to the model.
  - **Address Properties**: If the test accesses the accessor like an attribute (e.g., instance.value) instead of calling it like a method (e.g., instance.value()), it means the accessor is a property. In this case, put it after the "Must have the property: " prefix.

#### How to write reasoning of the hints:

In the reasoning, please explain the connection between the golden source and golden tests, that made you decide to include the signature in the hints, please mention the specific usage in test file, mentioning the test file path and the test name, and mention the corresponding change in the non-test file, mentioning the file path and function/class/variable change, for example:

> The signature add(x, y) was needed to added to the hints, because it is in a non test file `src/add.ts` and it is used in the test name "make sure that add works with negative numbers" in the test file `src/__tests__/add.ts` and it changed from add_numbers to only add.

### Example of Reasoning for Accepted Tasks:

- **Issue Clarity**: The issue clarity is 0, because from the issue description, we can understand that we need to edit the functionality X so we can fix the bug of having multiple jobs cancelling each other.
- **Test To Issue Alignment**: The test to issue alignment is 1 because we have 3 fail_to_pass tests, while the tests test_x, test_y, test_z are enough to make sure that the issue is fixed, it would have been nicer to have a test to check the edge case x and y and z.
- **Verdict summary**: the task is accepted because issue is clear (score 0), test to issue alignment is 1, the fail_to_pass tests and pass_to_pass tests align with the logs.

---

## Final Verdict

**Reject if:**

- issue_clarity_score >= 2 OR test_to_issue_alignment_score >= 2 (because this means that the task is improper)

**Otherwise Accept**

---

## Deprecated Checks

### ~~DEPRECATED~~ (you no longer have to check):

1. **Empty agent_patch.diff, report.json or \_post_agent_patch.log**

   - You should no longer reject a task if any of the above files are empty. Ignore these files at all and don't even open them.

2. **PR is the last one to reference the issue**

   - You should no longer check for other PRs linked to the issue.

3. **PR linked to multiple issues**

   - Ignore if a PR is linked to multiple issues. As long as Test to Issue Alignment Score is good, the task is good.

4. **Including referenced issues in hints context**

   - You no longer have to include the referenced issues in the hint context, though, you still need to consider them when giving Issue Clarity Score and Test to Issue Alignment Score.

5. **Issue description contains non-English text**

   - You should no longer reject a task based on this criteria. You should translate the issue description before giving any score.

6. **Reject if MSFT failed**
   - All the new batches have the deliverable_url in the metadata (which is accessible from RLHF tool), so you don't even have to open the SWE Bench tool as we used to do.

---

## Common Mistakes During Review

### ❌ Wrong Assumptions:

1. **"All tests in test_patch needs to be found in after"** → **WRONG**

   - ✅ It's okay that some tests in test_patch not found in after

2. **"All tests in base should pass"** → **WRONG**

   - ✅ It's okay that some tests in base are failing, as long as they're not included in F2P / P2P

3. **"Before: it should run all the tests"** → **WRONG**

   - ✅ It's okay if it has build error, as the test_patch might have dependency on gold_patch, which will cause build error.

4. **"Before: some tests should be failing, some tests should be passing"** → **WRONG**

   - ✅ It's okay if all of them are failing or it fails to run

5. **"All tests in after should pass"** → **WRONG**

   - ✅ It's okay that some tests in base are failing, as long as they're not included in F2P / P2P

6. **"All tests in F2P should be in test_patch"** → **WRONG**
   - ✅ Some bug fixing PRs actually have existing F2P.
