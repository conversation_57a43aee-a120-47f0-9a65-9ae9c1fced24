---
type: "manual"
---

# Data Quality Assurance Guidelines

**TL;DR:** Apply the Swebench Trainer Workflow Guideline - V2 guideline.

## Overview

The Quality Assurance (QA) team is responsible for making sure that the disagreements between annotation parties are settled.

### Annotation Parties

There are multiple parties involved in the annotation process:

1. **Trainer 1**: A developer
2. **Trainer 2**: A developer
3. **Pod lead or trainer**: An annotator that takes Trainer 1 and Trainer 2 as reference for their verdict
4. **Calibrator**: Calibrator and trainer 3 have the same verdict in most circumstances, hence they can be used interchangeably

### Task Classifications

Each party has its own opinion on what the classification of a task should be: either **ACCEPT** or **REJECT**.

The QA team is in charge of making sense of these opinions and output a category per task:

- **ACCEPT**: The task is sent to the customer
- **REJECT**: The task is invalidated and never used
- **REWORK**: The task is sent back to the annotation team (an option in some batches)

The QA team is aligned with the **Swebench Trainer Workflow Guideline - V2** guidelines just like the annotation parties are. This means that QA team members will follow the guideline to settle disagreements.

---

## Dependent Data

We refer to **dependent data** as the one where a trainer's perspective is dependent on other trainer's annotation. For example, in the most common of cases trainer 3's outcome depends on the annotations made by trainer 1 and 2, hence trainer 3 is dependent. Given this dependency, trainer 3's annotation has a heavier weight than their peers.

### How to Approach Evaluation of Dependent Data:

1. **Revise early rejections**

   - Check if there is any early rejection and whether this early rejection is justified (see the list below)
   - An invalid rejection means that a status of **REWORK** has to be assigned

2. **Handle disagreements between Calibrator/Trainer 3 and Trainers 1 & 2**

   - If there is one disagreement, this disagreement has to be justified
   - A **disagreement** is when an annotation made by party X is different from the annotation made by party Y. This could be in any field: final verdict, hint is needed, test to issue alignment, issue clarity, etc.
   - Conversely, an **agreement** is when both annotations match

3. **Verify agreements are for the same reasons**

   - If there is an agreement, this has to be because of the same reasons
   - There could be instances where there is an agreement between parties for disjoint reasons. If this is the case, mark it as **REWORK**

4. **Resolve disagreements**

   - If there is a disagreement between parties, we need to judge which party is correct and use its field value
   - This involves revisiting the parts referenced by the trainers, making sure what they state is correct and applying all the rules from the trainer guidelines in case a verdict change is in order
   - If a resolution of disagreements results in a verdict change by trainer 3, send it back to **REWORK**
   - Otherwise, use the verdict of the party you chose

5. **Special case for rework batches**
   - If the task belongs to a rework batch then it is not possible to assign the REWORK status
   - The final verdict has to be given by the QA member following the Swebench Trainer Workflow Guideline - V2 guideline

---

## Invalid Rejection Reasons

The following are considered invalid reasons for rejection:

1. <20 LOC
2. Having failed tests in base/after, but didn't mention which F2P/P2P test
3. Test didn't run in before
4. report.json is empty
5. Agent patch is empty
6. Post_agent_log is empty
7. MSFT failed
8. Agent run failed
9. Too many code/file (without saying how many files are there)
10. Multiple issues are closed
11. Multiple PRs linked to one issue
12. PR is referenced to multiple issues
13. Issue and PR contain non-English language
    - **Rationale**: will be missing annotation results
14. If there is no explain issue
15. Issue is open
16. Duplicates issue
17. F2P/P2P is wrong, but not saying which rule # is violated (see rules in trainer guidelines)

---

## Independent Data

We refer to **independent data** as the one where a trainer's perspective is independent of other trainer's annotation.

### How to Approach Evaluation of Independent Data:

1. **Revise early rejections across all 3 trainers**

   - Check if there is any early rejection and whether this early rejection is justified on all 3 trainers (see the list above)
   - If the rejection is not justified, use your own analysis to fill up the blanks of the trainer

2. **Handle disagreements between trainers**

   - If there is one disagreement between any of the trainers, this disagreement has to be justified
   - A **disagreement** is when an annotation made by party X is different from the annotation made by party Y. This could be in any field: final verdict, hint is needed, test to issue alignment, issue clarity, etc.
   - Conversely, an **agreement** is when both annotations match

3. **Verify agreements are for the same reasons**

   - If there is an agreement, this has to be because of the same reasons
   - There could be instances where there is an agreement between parties for disjoint reasons. In this case, your evaluation weighs the most

4. **Resolve disagreements**

   - If there is a disagreement between parties, we need to judge which party is correct and use its field value
   - This involves revisiting the parts referenced by the trainers, making sure what they state is correct and applying all the rules from the trainer guidelines in case a verdict change is in order
   - Otherwise, use the verdict of the party you chose

5. **Document hints**

   - You need to write down the need for a hint and the hint value

6. **Play devil's advocate**

   - In your annotation, it is expected that you play "devil's advocate" and try to find reasons on why the verdict for each trainer should be different

7. **Special case for rework batches**
   - If the task belongs to a rework batch then it is not possible to assign the REWORK status
   - The final verdict has to be given by the QA member following the Swebench Trainer Workflow Guideline - V2 guideline
